'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Calendar,
  Search,
  Clock,
  MapPin,
  Users,
  Building,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  AlertCircle,
  Plus
} from 'lucide-react';
import Link from 'next/link';
import { useBookings } from '@/lib/hooks/use-bookings';
import { DateTime } from 'luxon';

export default function MyBookingsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [resourceTypeFilter, setResourceTypeFilter] = useState<string>('all');

  // Fetch user's bookings (this would need to be filtered by current user in the API)
  const { data: bookingsData, isLoading } = useBookings({
    page: 1,
    limit: 50,
    ...(statusFilter !== 'all' && { status: statusFilter as any }),
    ...(resourceTypeFilter !== 'all' && { resourceType: resourceTypeFilter as any })
  });

  const formatDateTime = (date: Date | string) => {
    return DateTime.fromJSDate(new Date(date)).toFormat('dd/MM/yyyy HH:mm');
  };

  const formatTime = (date: Date | string) => {
    return DateTime.fromJSDate(new Date(date)).toFormat('HH:mm');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'NO_SHOW':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return <CheckCircle className="h-4 w-4" />;
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4" />;
      case 'CANCELLED':
        return <XCircle className="h-4 w-4" />;
      case 'NO_SHOW':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getResourceIcon = (resourceType: string) => {
    return resourceType === 'MEETING_ROOM' ? <Building className="h-4 w-4" /> : <Users className="h-4 w-4" />;
  };

  const filteredBookings = bookingsData?.data?.filter((booking: any) => {
    const matchesSearch = !searchTerm ||
      booking.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.description?.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  }) || [];

  const renderBookingCard = (booking: any) => (
    <Card key={booking.id} className="border-0 shadow-sm rounded-xl bg-white/80 backdrop-blur-sm hover:shadow-md transition-all duration-200">
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              {getResourceIcon(booking.resourceType)}
              <h3 className="font-semibold text-gray-900">{booking.title}</h3>
              <Badge className={`${getStatusColor(booking.status)} rounded-full px-2 py-1 text-xs font-medium border`}>
                <span className="flex items-center gap-1">
                  {getStatusIcon(booking.status)}
                  {booking.status}
                </span>
              </Badge>
            </div>

            {booking.description && (
              <p className="text-sm text-gray-600 mb-3">{booking.description}</p>
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>{formatDateTime(booking.startDate)}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                <span>{formatTime(booking.startDate)} - {formatTime(booking.endDate)}</span>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                <span>
                  {booking.resourceType === 'MEETING_ROOM'
                    ? booking.meetingRoom?.name || 'Sala de Reunión'
                    : booking.desk?.name || 'Escritorio'
                  }
                </span>
              </div>
            </div>

            {booking.attendees && (booking.attendees.internal?.length > 0 || booking.attendees.external?.length > 0) && (
              <div className="mt-3 flex items-center gap-2 text-sm text-gray-600">
                <Users className="h-4 w-4" />
                <span>
                  {(booking.attendees.internal?.length || 0) + (booking.attendees.external?.length || 0)} asistentes
                </span>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="rounded-lg" asChild>
              <Link href={`/dashboard/bookings/${booking.id}`}>
                <MoreHorizontal className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
        <div className="max-w-7xl mx-auto p-6 space-y-6">
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <Card key={i} className="border-0 shadow-sm rounded-xl">
                <CardContent className="p-6">
                  <div className="space-y-3">
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                    <div className="grid grid-cols-3 gap-3">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Mis Reservas</h1>
            <p className="text-gray-600">
              Gestiona tus reservas personales y las que tienes como invitado
            </p>
          </div>
          <div className="flex gap-2">
            <Button asChild className="bg-purple-600 hover:bg-purple-700 rounded-xl">
              <Link href="/dashboard/rooms">
                <Plus className="mr-2 h-4 w-4" />
                Nueva Reserva
              </Link>
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card className="border-0 shadow-sm rounded-xl bg-white/80 backdrop-blur-sm">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Buscar mis reservas..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40 rounded-xl border-gray-200">
                    <SelectValue placeholder="Estado" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos los estados</SelectItem>
                    <SelectItem value="CONFIRMED">Confirmado</SelectItem>
                    <SelectItem value="COMPLETED">Completado</SelectItem>
                    <SelectItem value="CANCELLED">Cancelado</SelectItem>
                    <SelectItem value="NO_SHOW">No Show</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={resourceTypeFilter} onValueChange={setResourceTypeFilter}>
                  <SelectTrigger className="w-40 rounded-xl border-gray-200">
                    <SelectValue placeholder="Tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Todos los tipos</SelectItem>
                    <SelectItem value="MEETING_ROOM">Salas</SelectItem>
                    <SelectItem value="DESK">Escritorios</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bookings List */}
        {filteredBookings.length === 0 ? (
          <div className="text-center py-12">
            <Calendar className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-4 text-lg font-semibold text-gray-900">No tienes reservas</h3>
            <p className="mt-2 text-gray-600">
              No tienes reservas actualmente. ¡Crea tu primera reserva!
            </p>
            <Button asChild className="mt-4 bg-purple-600 hover:bg-purple-700">
              <Link href="/dashboard/rooms/calendar">
                <Plus className="mr-2 h-4 w-4" />
                Nueva Reserva
              </Link>
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredBookings.map(renderBookingCard)}
          </div>
        )}
      </div>
    </div>
  );
}
