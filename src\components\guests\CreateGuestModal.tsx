'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Calendar, Clock, Mail, User, Building } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { useState } from 'react';
import { DateTime } from 'luxon';
import { useCreateGuest, CreateGuestData } from '@/lib/hooks/use-guests';

interface CreateGuestModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface FormData {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  visitDate: string;
  visitTime: string;
  instructions?: string;
}

export function CreateGuestModal({ isOpen, onClose }: CreateGuestModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const createGuestMutation = useCreateGuest();

  const form = useForm<FormData>({
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      company: '',
      visitDate: DateTime.now().toFormat('yyyy-MM-dd'),
      visitTime: DateTime.now().toFormat('HH:mm'),
      instructions: '',
    },
  });

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    try {
      const guestData: CreateGuestData = {
        name: data.name,
        email: data.email,
        phone: data.phone,
        company: data.company,
        visitDate: data.visitDate,
        visitTime: data.visitTime,
        instructions: data.instructions,
      };

      await createGuestMutation.mutateAsync(guestData);
      form.reset();
      onClose();
    } catch (error) {
      console.error('Error creating guest:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5 text-purple-600" />
            Crear Invitación General
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Información del Invitado */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2">
                <User className="h-4 w-4" />
                Información del Invitado
              </h3>

              <FormField
                control={form.control}
                name="name"
                rules={{ required: 'El nombre es requerido' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nombre completo *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Nombre del invitado"
                        {...field}
                        className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                rules={{
                  required: 'El email es requerido',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Email inválido'
                  }
                }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email *</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                          className="pl-10 rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Teléfono</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="****** 567 8900"
                          {...field}
                          className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="company"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Empresa</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <Input
                            placeholder="Nombre de la empresa"
                            {...field}
                            className="pl-10 rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Información de la Visita */}
            <div className="space-y-4">
              <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Información de la Visita
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="visitDate"
                  rules={{ required: 'La fecha de visita es requerida' }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Fecha de visita *</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                          min={DateTime.now().toFormat('yyyy-MM-dd')}
                          className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="visitTime"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Hora estimada</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <Input
                            type="time"
                            {...field}
                            className="pl-10 rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="instructions"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Instrucciones adicionales</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Instrucciones especiales para la visita..."
                        {...field}
                        className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500 min-h-[80px]"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Buttons */}
            <div className="flex justify-end gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
                className="rounded-xl"
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="rounded-xl bg-purple-600 hover:bg-purple-700"
              >
                {isSubmitting ? 'Creando...' : 'Crear Invitación'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
