/**
 * Hook to get current user with role information
 */

import { useQuery } from '@tanstack/react-query';
import { useCurrentUser } from '@/context/user-provider';
import server from '@/app/api/server';

interface UserWithRole {
  id: string;
  name: string;
  email: string;
  role: 'USER' | 'ADMIN' | 'RECEPTIONIST';
  department?: string;
  isActive: boolean;
  image?: string;
  emailVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Hook to get current user with role information from the database
 * Falls back to context user if API call fails
 */
export function useCurrentUserWithRole() {
  const { user: contextUser } = useCurrentUser();

  const { data: userWithRole, isLoading, error } = useQuery({
    queryKey: ['current-user-with-role', contextUser.id],
    queryFn: async (): Promise<UserWithRole> => {
      try {
        // Try to get user with role from API
        const response = await server.api.users({ id: contextUser.id }).get();
        
        if (response.error) {
          throw new Error('Failed to fetch user with role');
        }

        return response.data;
      } catch (error) {
        // Fallback: assume USER role if API fails
        console.warn('Failed to fetch user role, defaulting to USER:', error);
        return {
          ...contextUser,
          role: 'USER' as const,
          department: undefined,
          isActive: true
        };
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1, // Only retry once
  });

  // Return user with role, or fallback to context user with USER role
  const user: UserWithRole = userWithRole || {
    ...contextUser,
    role: 'USER' as const,
    department: undefined,
    isActive: true
  };

  return {
    user,
    isLoading,
    error
  };
}
