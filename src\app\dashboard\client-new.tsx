'use client';

import { Dash<PERSON>Header, StatsCards, TodayBookings, MeetingRoomsList } from '@/components/dashboard';
import { useTodayBookings, useBookingStats } from '@/lib/hooks/use-bookings';
import { useMeetingRooms } from '@/lib/hooks/use-meeting-rooms';

export default function DashboardClientPage() {
  const { data: todayBookings, isLoading: loadingBookings } = useTodayBookings();
  const { data: stats, isLoading: loadingStats } = useBookingStats();
  const { data: meetingRooms, isLoading: loadingRooms } = useMeetingRooms({
    limit: 5,
    isActive: true
  });

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header */}
      <DashboardHeader />

      {/* Stats Cards */}
      <StatsCards
        todayBookingsCount={todayBookings?.data?.length || 0}
        totalBookings={stats?.data?.totalBookings || 0}
        totalRooms={meetingRooms?.pagination?.total || 0}
        confirmedBookings={stats?.data?.confirmedBookings || 0}
        isLoadingBookings={loadingBookings}
        isLoadingStats={loadingStats}
        isLoadingRooms={loadingRooms}
      />

      <div className="grid gap-6 md:grid-cols-2">
        {/* Today's Bookings */}
        <TodayBookings
          bookings={todayBookings?.data || []}
          isLoading={loadingBookings}
        />

        {/* Meeting Rooms */}
        <MeetingRoomsList
          rooms={meetingRooms?.data || []}
          isLoading={loadingRooms}
        />
      </div>
    </div>
  );
}
