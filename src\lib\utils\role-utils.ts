/**
 * Utility functions for role-based access control
 */

export type UserRole = 'USER' | 'ADMIN' | 'RECEPTIONIST';

/**
 * Check if user has admin role
 * @param userRole - The user's role
 * @returns true if user is admin, false otherwise
 */
export function isAdmin(userRole: UserRole): boolean {
  return userRole === 'ADMIN';
}

/**
 * Check if user has receptionist role
 * @param userRole - The user's role
 * @returns true if user is receptionist, false otherwise
 */
export function isReceptionist(userRole: UserRole): boolean {
  return userRole === 'RECEPTIONIST';
}

/**
 * Check if user has admin or receptionist role
 * @param userRole - The user's role
 * @returns true if user is admin or receptionist, false otherwise
 */
export function isAdminOrReceptionist(userRole: UserRole): boolean {
  return isAdmin(userRole) || isReceptionist(userRole);
}

/**
 * Check if user can access admin features
 * @param userRole - The user's role
 * @returns true if user can access admin features, false otherwise
 */
export function canAccessAdminFeatures(userRole: UserRole): boolean {
  return isAdmin(userRole);
}

/**
 * Check if user can manage rooms (create, edit, delete)
 * @param userRole - The user's role
 * @returns true if user can manage rooms, false otherwise
 */
export function canManageRooms(userRole: UserRole): boolean {
  return isAdmin(userRole);
}

/**
 * Check if user can view all bookings (admin calendar)
 * @param userRole - The user's role
 * @returns true if user can view all bookings, false otherwise
 */
export function canViewAllBookings(userRole: UserRole): boolean {
  return isAdmin(userRole);
}

/**
 * Get user role display name
 * @param userRole - The user's role
 * @returns Display name for the role
 */
export function getRoleDisplayName(userRole: UserRole): string {
  switch (userRole) {
    case 'ADMIN':
      return 'Administrador';
    case 'RECEPTIONIST':
      return 'Recepcionista';
    case 'USER':
      return 'Usuario';
    default:
      return 'Usuario';
  }
}
