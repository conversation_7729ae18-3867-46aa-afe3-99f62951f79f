'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import Link from 'next/link';
import { DashboardHeaderProps } from './types';

export function DashboardHeader({ className = '' }: DashboardHeaderProps) {
  return (
    <div className={`flex items-center justify-between ${className}`}>
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">
          ¡Bienvenido de vuelta! Esto es lo que está pasando hoy.
        </p>
      </div>
      <div className="flex gap-2">
        <Button asChild className="bg-purple-600 hover:bg-purple-700">
          <Link href="/dashboard/rooms/calendar">
            <Plus className="mr-2 h-4 w-4" />
            Nueva Reserva
          </Link>
        </Button>
      </div>
    </div>
  );
}
