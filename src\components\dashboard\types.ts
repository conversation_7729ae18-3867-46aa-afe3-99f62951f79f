export interface DashboardHeaderProps {
  className?: string;
}

export interface StatsCardsProps {
  todayBookingsCount: number;
  totalBookings: number;
  totalRooms: number;
  confirmedBookings: number;
  isLoadingBookings?: boolean;
  isLoadingStats?: boolean;
  isLoadingRooms?: boolean;
  className?: string;
}

export interface TodayBookingsProps {
  bookings: any[];
  isLoading?: boolean;
  className?: string;
}

export interface MeetingRoomsListProps {
  rooms: any[];
  isLoading?: boolean;
  className?: string;
}

export interface BookingItemProps {
  booking: any;
  formatTime: (date: Date | string) => string;
  getStatusColor: (status: string) => string;
}

export interface RoomItemProps {
  room: any;
}
