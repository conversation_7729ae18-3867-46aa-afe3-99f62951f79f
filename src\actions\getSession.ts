// src/actions/getSession.ts
"use server";
import { auth } from '@/lib/auth';
import { Session, User } from '@prisma/client';
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';
// import { User, Session } from 'better-auth'

export const getSession = async ({ shouldRedirect = true }: { shouldRedirect?: boolean } = {}) => {

  const headersList = await headers();
  const session = await auth.api.getSession({
    headers: headersList,
  });

  if (!session && shouldRedirect) {
    return redirect('/');
  }

  return session! as {
    user: User;
    session: Session;
  }
};
