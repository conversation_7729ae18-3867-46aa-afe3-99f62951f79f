import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import server from '@/app/api/server';
import { toast } from 'sonner';

// Query Keys
export const guestKeys = {
  all: ['guests'] as const,
  lists: () => [...guestKeys.all, 'list'] as const,
  list: (filters: GuestFilters) => [...guestKeys.lists(), filters] as const,
  details: () => [...guestKeys.all, 'detail'] as const,
  detail: (id: string) => [...guestKeys.details(), id] as const,
  daily: (date?: string) => [...guestKeys.all, 'daily', date] as const,
};

// Types
export interface GuestFilters {
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
  status?: 'PENDING' | 'CHECKED_IN' | 'CHECKED_OUT' | 'NO_SHOW';
  hostId?: string;
  visitDate?: string;
  search?: string;
}

export interface CreateGuestData {
  name: string;
  email?: string;
  phone?: string;
  company?: string;
  visitDate: string;
  visitTime: string;
  bookingId?: string;
  instructions?: string;
}

export interface UpdateGuestData {
  name?: string;
  email?: string;
  phone?: string;
  company?: string;
  visitDate?: string;
  visitTime?: string;
  bookingId?: string;
  instructions?: string;
}

export interface DailyGuestsFilters {
  date?: string;
  status?: 'PENDING' | 'CHECKED_IN' | 'CHECKED_OUT' | 'NO_SHOW';
  hostId?: string;
}

// Hooks
export function useGuests(filters: GuestFilters = {}) {
  return useQuery({
    queryKey: guestKeys.list(filters),
    queryFn: async () => {
      const response = await server.api.guests.get({
        query: filters
      });

      if (response.error) {
        throw new Error('Failed to fetch guests');
      }

      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useGuest(id: string) {
  return useQuery({
    queryKey: guestKeys.detail(id),
    queryFn: async () => {
      const response = await server.api.guests({ id }).get();

      if (response.error) {
        throw new Error('Failed to fetch guest');
      }

      return response.data;
    },
    enabled: !!id,
    staleTime: 2 * 60 * 1000,
  });
}

export function useDailyGuests(filters: DailyGuestsFilters = {}) {
  return useQuery({
    queryKey: guestKeys.daily(filters.date),
    queryFn: async () => {
      const response = await server.api.guests.daily.get({
        query: filters
      });

      if (response.error) {
        throw new Error('Failed to fetch daily guests');
      }

      return response.data;
    },
    staleTime: 1 * 60 * 1000, // 1 minute for daily data
  });
}

export function useCreateGuest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateGuestData) => {
      const response = await server.api.guests.post(data);

      if (response.error) {
        // throw new Error(response.error.value.message || 'Failed to create guest');
        throw new Error(response.error.value.message || 'Failed to create guest');
      }

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: guestKeys.all });
      toast.success('Invitado creado exitosamente');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useUpdateGuest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateGuestData }) => {
      const response = await server.api.guests({ id }).put(data);

      if (response.error) {
        throw new Error(response.error.value.message || 'Failed to update guest');
      }

      return response.data;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: guestKeys.all });
      queryClient.invalidateQueries({ queryKey: guestKeys.detail(id) });
      toast.success('Invitado actualizado exitosamente');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useDeleteGuest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await server.api.guests({ id }).delete();

      if (response.error) {
        throw new Error(response.error.value.message || 'Failed to delete guest');
      }

      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: guestKeys.all });
      toast.success('Invitado eliminado exitosamente');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useCheckInGuest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await server.api.guests({ id })['check-in'].post();

      if (response.error) {
        throw new Error(response.error.value.message || 'Failed to check in guest');
      }

      return response.data;
    },
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: guestKeys.all });
      queryClient.invalidateQueries({ queryKey: guestKeys.detail(id) });
      toast.success('Invitado registrado exitosamente');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}

export function useCheckOutGuest() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await server.api.guests({ id })['check-out'].post();

      if (response.error) {
        throw new Error(response.error.value.message || 'Failed to check out guest');
      }

      return response.data;
    },
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: guestKeys.all });
      queryClient.invalidateQueries({ queryKey: guestKeys.detail(id) });
      toast.success('Invitado dado de salida exitosamente');
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
}
