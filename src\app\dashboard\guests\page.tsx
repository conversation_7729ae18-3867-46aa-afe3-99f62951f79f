'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Users,
  Search,
  Plus,
  Calendar,
  Clock,
  Mail,
  Building,
  User,
  UserCheck,
  UserX,
  MoreHorizontal
} from 'lucide-react';
import { DateTime } from 'luxon';
import { useGuests, GuestFilters } from '@/lib/hooks/use-guests';
import { CreateGuestModal } from '@/components/guests/CreateGuestModal';
import { GuestDetailsModal } from '@/components/guests/GuestDetailsModal';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useCheckInGuest, useCheckOutGuest } from '@/lib/hooks/use-guests';

export default function GuestsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('today');
  const [customStartDate, setCustomStartDate] = useState('');
  const [customEndDate, setCustomEndDate] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedGuestId, setSelectedGuestId] = useState<string | null>(null);

  const checkInMutation = useCheckInGuest();
  const checkOutMutation = useCheckOutGuest();

  // Prepare filters based on current state
  const getFilters = (): GuestFilters => {
    const filters: GuestFilters = {
      limit: 50,
    };

    if (searchTerm) {
      filters.search = searchTerm;
    }

    if (statusFilter !== 'all') {
      filters.status = statusFilter as any;
    }

    // Date filtering
    const today = DateTime.now();
    switch (dateFilter) {
      case 'today':
        filters.visitDate = today.toFormat('yyyy-MM-dd');
        break;
      case 'week':
        filters.startDate = today.startOf('week').toFormat('yyyy-MM-dd');
        filters.endDate = today.endOf('week').toFormat('yyyy-MM-dd');
        break;
      case 'month':
        filters.startDate = today.startOf('month').toFormat('yyyy-MM-dd');
        filters.endDate = today.endOf('month').toFormat('yyyy-MM-dd');
        break;
      case 'custom':
        if (customStartDate) filters.startDate = customStartDate;
        if (customEndDate) filters.endDate = customEndDate;
        break;
    }

    return filters;
  };

  const { data: guestsData, isLoading } = useGuests(getFilters());

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'CHECKED_IN':
        return 'bg-green-100 text-green-800';
      case 'CHECKED_OUT':
        return 'bg-blue-100 text-blue-800';
      case 'NO_SHOW':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'Pendiente';
      case 'CHECKED_IN':
        return 'Registrado';
      case 'CHECKED_OUT':
        return 'Salida';
      case 'NO_SHOW':
        return 'No se presentó';
      default:
        return status;
    }
  };

  const formatDate = (date: string) => {
    return DateTime.fromISO(date).toFormat('dd/MM/yyyy');
  };

  const formatTime = (time: string) => {
    return time || '--:--';
  };

  const getGuestType = (guest: any) => {
    if (guest.booking) {
      const resource = guest.booking.meetingRoom || guest.booking.desk;
      return {
        type: 'booking',
        text: `${guest.booking.meetingRoom ? 'Sala' : 'Escritorio'}: ${resource?.name || 'N/A'}`,
        icon: guest.booking.meetingRoom ? Building : User
      };
    }
    return {
      type: 'general',
      text: 'Invitado General',
      icon: Users
    };
  };

  const handleCheckIn = async (guestId: string) => {
    try {
      await checkInMutation.mutateAsync(guestId);
    } catch (error) {
      console.error('Error checking in guest:', error);
    }
  };

  const handleCheckOut = async (guestId: string) => {
    try {
      await checkOutMutation.mutateAsync(guestId);
    } catch (error) {
      console.error('Error checking out guest:', error);
    }
  };

  return (
    <div className="flex flex-col gap-6 p-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gestión de Invitados</h1>
          <p className="text-gray-600 mt-1">
            Administra invitados externos y de reservas
          </p>
        </div>
        <Button
          onClick={() => setIsCreateModalOpen(true)}
          className="rounded-xl bg-purple-600 hover:bg-purple-700 text-white shadow-lg"
        >
          <Plus className="mr-2 h-4 w-4" />
          Crear Invitación
        </Button>
      </div>

      {/* Filters */}
      <Card className="border-0 shadow-sm rounded-xl bg-white/80 backdrop-blur-sm">
        <CardContent className="p-6">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por nombre, email o empresa..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40 rounded-xl border-gray-200">
                  <SelectValue placeholder="Estado" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los estados</SelectItem>
                  <SelectItem value="PENDING">Pendiente</SelectItem>
                  <SelectItem value="CHECKED_IN">Registrado</SelectItem>
                  <SelectItem value="CHECKED_OUT">Salida</SelectItem>
                  <SelectItem value="NO_SHOW">No se presentó</SelectItem>
                </SelectContent>
              </Select>

              <Select value={dateFilter} onValueChange={setDateFilter}>
                <SelectTrigger className="w-40 rounded-xl border-gray-200">
                  <SelectValue placeholder="Fecha" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Hoy</SelectItem>
                  <SelectItem value="week">Esta semana</SelectItem>
                  <SelectItem value="month">Este mes</SelectItem>
                  <SelectItem value="custom">Personalizado</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Custom Date Range */}
          {dateFilter === 'custom' && (
            <div className="flex gap-4 mt-4">
              <div className="flex-1">
                <Input
                  type="date"
                  placeholder="Fecha inicio"
                  value={customStartDate}
                  onChange={(e) => setCustomStartDate(e.target.value)}
                  className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                />
              </div>
              <div className="flex-1">
                <Input
                  type="date"
                  placeholder="Fecha fin"
                  value={customEndDate}
                  onChange={(e) => setCustomEndDate(e.target.value)}
                  className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center gap-4 text-sm text-gray-600">
        <span>
          {guestsData?.data?.length || 0} invitados encontrados
        </span>
        {dateFilter === 'today' && (
          <Badge variant="outline" className="text-purple-600 border-purple-200">
            <Calendar className="mr-1 h-3 w-3" />
            Hoy
          </Badge>
        )}
      </div>

      {/* Guests List */}
      <Card className="border-0 shadow-sm rounded-xl bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5 text-purple-600" />
            Lista de Invitados
          </CardTitle>
          <CardDescription>
            Invitados externos y de reservas
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center justify-between p-4 border rounded-xl">
                  <div className="flex items-center space-x-4">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-[200px]" />
                      <Skeleton className="h-3 w-[150px]" />
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Skeleton className="h-6 w-20" />
                    <Skeleton className="h-8 w-8" />
                  </div>
                </div>
              ))}
            </div>
          ) : guestsData?.data?.length === 0 ? (
            <div className="text-center py-12">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">No hay invitados</h3>
              <p className="mt-2 text-gray-500">
                No se encontraron invitados con los filtros seleccionados.
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {guestsData?.data?.map((guest: any) => {
                const guestType = getGuestType(guest);
                const IconComponent = guestType.icon;

                return (
                  <div
                    key={guest.id}
                    className="flex items-center justify-between p-4 border border-gray-100 rounded-xl hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                          <User className="h-5 w-5 text-purple-600" />
                        </div>
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <h3 className="text-sm font-medium text-gray-900 truncate">
                            {guest.name}
                          </h3>
                          <Badge className={getStatusColor(guest.status)}>
                            {getStatusText(guest.status)}
                          </Badge>
                        </div>

                        <div className="flex items-center gap-4 mt-1 text-xs text-gray-500">
                          <span className="flex items-center gap-1">
                            <Mail className="h-3 w-3" />
                            {guest.email || 'Sin email'}
                          </span>
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {formatDate(guest.visitDate)}
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {formatTime(guest.visitTime)}
                          </span>
                        </div>

                        <div className="flex items-center gap-1 mt-1">
                          <IconComponent className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-500">{guestType.text}</span>
                        </div>

                        {guest.company && (
                          <div className="flex items-center gap-1 mt-1">
                            <Building className="h-3 w-3 text-gray-400" />
                            <span className="text-xs text-gray-500">{guest.company}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {guest.status === 'PENDING' && (
                        <Button
                          size="sm"
                          onClick={() => handleCheckIn(guest.id)}
                          disabled={checkInMutation.isPending}
                          className="rounded-lg bg-green-600 hover:bg-green-700 text-white"
                        >
                          <UserCheck className="h-3 w-3 mr-1" />
                          Registrar
                        </Button>
                      )}

                      {guest.status === 'CHECKED_IN' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleCheckOut(guest.id)}
                          disabled={checkOutMutation.isPending}
                          className="rounded-lg border-blue-200 text-blue-600 hover:bg-blue-50"
                        >
                          <UserX className="h-3 w-3 mr-1" />
                          Salida
                        </Button>
                      )}

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => setSelectedGuestId(guest.id)}>
                            Ver detalles
                          </DropdownMenuItem>
                          <DropdownMenuItem>Editar</DropdownMenuItem>
                          <DropdownMenuItem className="text-red-600">
                            Eliminar
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Guest Modal */}
      <CreateGuestModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
      />

      {/* Guest Details Modal */}
      <GuestDetailsModal
        isOpen={!!selectedGuestId}
        onClose={() => setSelectedGuestId(null)}
        guestId={selectedGuestId}
      />
    </div>
  );
}
