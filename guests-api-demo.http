### OCN House - Guests API Demo
### Base URL: http://localhost:4002/api

### ============================================
### GUESTS ENDPOINTS - COMPLETE WORKFLOW
### ============================================

### 1. Get all guests (with pagination and filters)
GET http://localhost:4002/api/guests
Content-Type: application/json

### 2. Get guests with search filter
GET http://localhost:4002/api/guests?search=pedro&page=1&limit=10
Content-Type: application/json

### 3. Get guests by status
GET http://localhost:4002/api/guests?status=PENDING
Content-Type: application/json

### 4. Get guests by date range
GET http://localhost:4002/api/guests?startDate=2024-01-01&endDate=2024-12-31
Content-Type: application/json

### 5. Get guests for specific date
GET http://localhost:4002/api/guests?visitDate=2024-07-04
Content-Type: application/json

### ============================================
### CREATE GENERAL GUEST INVITATION (No booking)
### ============================================

### 6. Create general guest invitation
POST http://localhost:4002/api/guests
Content-Type: application/json
x-user-id: user123

{
  "name": "Pedro Martinez",
  "email": "<EMAIL>",
  "phone": "****** 567 8900",
  "company": "Tech Solutions Inc",
  "visitDate": "2024-07-04",
  "visitTime": "14:30",
  "instructions": "Invitado general para reunión informal"
}

### 7. Create guest invitation with minimal data
POST http://localhost:4002/api/guests
Content-Type: application/json
x-user-id: user123

{
  "name": "Ana García",
  "email": "<EMAIL>",
  "visitDate": "2024-07-04",
  "visitTime": "10:00"
}

### ============================================
### CREATE GUEST INVITATION FOR BOOKING
### ============================================

### 8. Create guest invitation linked to a booking
POST http://localhost:4002/api/guests
Content-Type: application/json
x-user-id: user123

{
  "name": "Carlos Rodriguez",
  "email": "<EMAIL>",
  "phone": "****** 123 4567",
  "company": "Design Studio",
  "visitDate": "2024-07-04",
  "visitTime": "15:00",
  "bookingId": "booking123",
  "instructions": "Invitado para reunión en Sala de Juntas A"
}

### ============================================
### DAILY GUESTS (Main feature for reception)
### ============================================

### 9. Get today's guests (default)
GET http://localhost:4002/api/guests/daily
Content-Type: application/json

### 10. Get guests for specific date
GET http://localhost:4002/api/guests/daily?date=2024-07-04
Content-Type: application/json

### 11. Get today's pending guests only
GET http://localhost:4002/api/guests/daily?status=PENDING
Content-Type: application/json

### 12. Get today's checked-in guests
GET http://localhost:4002/api/guests/daily?status=CHECKED_IN
Content-Type: application/json

### ============================================
### GUEST MANAGEMENT OPERATIONS
### ============================================

### 13. Get specific guest details
GET http://localhost:4002/api/guests/guest123
Content-Type: application/json
x-user-id: user123

### 14. Update guest information
PUT http://localhost:4002/api/guests/guest123
Content-Type: application/json
x-user-id: user123

{
  "name": "Pedro Martinez Updated",
  "phone": "****** 567 8901",
  "instructions": "Instrucciones actualizadas"
}

### 15. Check in guest (Reception workflow)
POST http://localhost:4002/api/guests/guest123/check-in
Content-Type: application/json

### 16. Check out guest (Reception workflow)
POST http://localhost:4002/api/guests/guest123/check-out
Content-Type: application/json

### 17. Mark guest as no-show
POST http://localhost:4002/api/guests/guest123/no-show
Content-Type: application/json

### 18. Delete guest invitation
DELETE http://localhost:4002/api/guests/guest123
Content-Type: application/json
x-user-id: user123

### ============================================
### BULK OPERATIONS
### ============================================

### 19. Bulk check-in multiple guests
POST http://localhost:4002/api/guests/bulk
Content-Type: application/json

{
  "guestIds": ["guest123", "guest456", "guest789"],
  "operation": "check-in",
  "reason": "Bulk check-in for morning visitors"
}

### 20. Bulk check-out multiple guests
POST http://localhost:4002/api/guests/bulk
Content-Type: application/json

{
  "guestIds": ["guest123", "guest456"],
  "operation": "check-out",
  "reason": "End of visit"
}

### 21. Bulk mark as no-show
POST http://localhost:4002/api/guests/bulk
Content-Type: application/json

{
  "guestIds": ["guest789"],
  "operation": "mark-no-show",
  "reason": "Did not arrive for scheduled visit"
}

### 22. Bulk delete guests
POST http://localhost:4002/api/guests/bulk
Content-Type: application/json

{
  "guestIds": ["guest999"],
  "operation": "delete",
  "reason": "Cancelled visits"
}

### ============================================
### TYPICAL RECEPTION WORKFLOW
### ============================================

### Step 1: Reception checks today's expected guests
GET http://localhost:4002/api/guests/daily?status=PENDING
Content-Type: application/json

### Step 2: Guest arrives, search by email
GET http://localhost:4002/api/guests/daily?search=<EMAIL>
Content-Type: application/json

### Step 3: Check in the guest
POST http://localhost:4002/api/guests/guest123/check-in
Content-Type: application/json

### Step 4: Later, check out the guest
POST http://localhost:4002/api/guests/guest123/check-out
Content-Type: application/json

### ============================================
### EMPLOYEE WORKFLOW - CREATE INVITATIONS
### ============================================

### Employee creates general invitation (no meeting room)
POST http://localhost:4002/api/guests
Content-Type: application/json
x-user-id: employee123

{
  "name": "Cliente Potencial",
  "email": "<EMAIL>",
  "company": "Empresa Cliente",
  "visitDate": "2024-07-05",
  "visitTime": "11:00",
  "instructions": "Visita comercial - mostrar oficinas"
}

### Employee creates invitation for meeting room booking
POST http://localhost:4002/api/guests
Content-Type: application/json
x-user-id: employee123

{
  "name": "Consultor Externo",
  "email": "<EMAIL>",
  "company": "Consultoría ABC",
  "visitDate": "2024-07-05",
  "visitTime": "14:00",
  "bookingId": "booking456",
  "instructions": "Reunión de consultoría en Sala de Juntas B"
}
