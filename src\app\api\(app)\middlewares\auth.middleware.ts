
// import { auth } from '@/lib/auth';
// import { Elysia } from 'elysia';

import { auth } from '@/lib/auth';
import Elysia from 'elysia';
import { Session, User } from '@prisma/client';

/** 
 * Auth middleware and extend context with user and session, 
 * this makes the authentication middleware and make organizationId available in all controllers
 * @param app Elysia instance
 */

export const authMiddleware = (app: Elysia) =>
  app.derive(async (ctx) => {

    // console.log('headers: ', ctx.request.headers);
    // console.log('cookies: ', ctx.request.headers.get('cookie'));
    const session = await auth.api.getSession({
      headers: ctx.request.headers
    });

    if (!session) {
      throw new Error('Not authorized')
    }

    return session as {
      user: User;
      session: Session;
    };
  })
