'use client'
import { Session, User } from '@prisma/client';
// import { User, Session } from 'better-auth'
// import context, and create a provider and useCurrentUser hook
// and create the provider
import { createContext, useContext, useState } from 'react';


interface UserContextType {
  user: User;
  setUser: (user: User) => void;
  session: Session;
  setSession: (session: Session) => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export function UserProvider({ children, user: initialUser, session: initialSession }: { children: React.ReactNode; user: User; session: Session }) {
  const [user, setUser] = useState(initialUser);
  const [session, setSession] = useState(initialSession);

  return (
    <UserContext.Provider value={{ user, setUser, session, setSession }}>
      {children}
    </UserContext.Provider>
  );
}

export function useCurrentUser() {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}
