'use client';

import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Calendar, 
  Clock, 
  Mail, 
  Phone, 
  Building, 
  User, 
  MapPin, 
  FileText,
  UserCheck,
  UserX,
  X
} from 'lucide-react';
import { DateTime } from 'luxon';
import { useGuest, useCheckInGuest, useCheckOutGuest } from '@/lib/hooks/use-guests';

interface GuestDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  guestId: string | null;
}

export function GuestDetailsModal({ isOpen, onClose, guestId }: GuestDetailsModalProps) {
  const { data: guest, isLoading } = useGuest(guestId || '');
  const checkInMutation = useCheckInGuest();
  const checkOutMutation = useCheckOutGuest();

  if (!guestId) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'CHECKED_IN':
        return 'bg-green-100 text-green-800';
      case 'CHECKED_OUT':
        return 'bg-blue-100 text-blue-800';
      case 'NO_SHOW':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'Pendiente';
      case 'CHECKED_IN':
        return 'Registrado';
      case 'CHECKED_OUT':
        return 'Salida';
      case 'NO_SHOW':
        return 'No se presentó';
      default:
        return status;
    }
  };

  const formatDate = (date: string) => {
    return DateTime.fromISO(date).toFormat('dd/MM/yyyy');
  };

  const formatTime = (time: string) => {
    return time || '--:--';
  };

  const formatDateTime = (date: string) => {
    return DateTime.fromISO(date).toFormat('dd/MM/yyyy HH:mm');
  };

  const getGuestType = (guest: any) => {
    if (guest?.booking) {
      const resource = guest.booking.meetingRoom || guest.booking.desk;
      return {
        type: 'booking',
        text: `${guest.booking.meetingRoom ? 'Sala de Reuniones' : 'Escritorio'}: ${resource?.name || 'N/A'}`,
        icon: guest.booking.meetingRoom ? Building : User,
        booking: guest.booking
      };
    }
    return {
      type: 'general',
      text: 'Invitado General',
      icon: User,
      booking: null
    };
  };

  const handleCheckIn = async () => {
    if (!guest?.data) return;
    try {
      await checkInMutation.mutateAsync(guest.data.id);
    } catch (error) {
      console.error('Error checking in guest:', error);
    }
  };

  const handleCheckOut = async () => {
    if (!guest?.data) return;
    try {
      await checkOutMutation.mutateAsync(guest.data.id);
    } catch (error) {
      console.error('Error checking out guest:', error);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <User className="h-5 w-5 text-purple-600" />
              Detalles del Invitado
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="space-y-4">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            </div>
          </div>
        ) : guest?.data ? (
          <div className="space-y-6">
            {/* Header with status */}
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">{guest.data.name}</h2>
                <p className="text-sm text-gray-500">
                  Invitado por: {guest.data.host?.name || 'N/A'}
                </p>
              </div>
              <Badge className={getStatusColor(guest.data.status)}>
                {getStatusText(guest.data.status)}
              </Badge>
            </div>

            <Separator />

            {/* Contact Information */}
            <div className="space-y-3">
              <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Información de Contacto
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <span>{guest.data.email || 'Sin email'}</span>
                </div>
                
                {guest.data.phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span>{guest.data.phone}</span>
                  </div>
                )}
                
                {guest.data.company && (
                  <div className="flex items-center gap-2 md:col-span-2">
                    <Building className="h-4 w-4 text-gray-400" />
                    <span>{guest.data.company}</span>
                  </div>
                )}
              </div>
            </div>

            <Separator />

            {/* Visit Information */}
            <div className="space-y-3">
              <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Información de la Visita
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span>Fecha: {formatDate(guest.data.visitDate)}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span>Hora: {formatTime(guest.data.visitTime)}</span>
                </div>
              </div>

              {/* Guest Type */}
              <div className="mt-3">
                {(() => {
                  const guestType = getGuestType(guest.data);
                  const IconComponent = guestType.icon;
                  
                  return (
                    <div className="flex items-center gap-2 text-sm">
                      <IconComponent className="h-4 w-4 text-gray-400" />
                      <span>{guestType.text}</span>
                    </div>
                  );
                })()}
              </div>

              {/* Booking Details if exists */}
              {(() => {
                const guestType = getGuestType(guest.data);
                if (guestType.booking) {
                  return (
                    <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Detalles de la Reserva</h4>
                      <div className="space-y-1 text-sm text-gray-600">
                        <p><strong>Título:</strong> {guestType.booking.title}</p>
                        {guestType.booking.description && (
                          <p><strong>Descripción:</strong> {guestType.booking.description}</p>
                        )}
                        <p><strong>Horario:</strong> {formatDateTime(guestType.booking.startDate)} - {formatDateTime(guestType.booking.endDate)}</p>
                      </div>
                    </div>
                  );
                }
                return null;
              })()}
            </div>

            {/* Instructions */}
            {guest.data.instructions && (
              <>
                <Separator />
                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-gray-900 flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Instrucciones
                  </h3>
                  <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                    {guest.data.instructions}
                  </p>
                </div>
              </>
            )}

            {/* Check-in/out Information */}
            {(guest.data.checkInTime || guest.data.checkOutTime) && (
              <>
                <Separator />
                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-gray-900">Registro de Entrada/Salida</h3>
                  <div className="space-y-2 text-sm">
                    {guest.data.checkInTime && (
                      <div className="flex items-center gap-2">
                        <UserCheck className="h-4 w-4 text-green-600" />
                        <span>Entrada: {formatDateTime(guest.data.checkInTime)}</span>
                      </div>
                    )}
                    {guest.data.checkOutTime && (
                      <div className="flex items-center gap-2">
                        <UserX className="h-4 w-4 text-blue-600" />
                        <span>Salida: {formatDateTime(guest.data.checkOutTime)}</span>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}

            <Separator />

            {/* Action Buttons */}
            <div className="flex justify-end gap-3">
              {guest.data.status === 'PENDING' && (
                <Button
                  onClick={handleCheckIn}
                  disabled={checkInMutation.isPending}
                  className="rounded-xl bg-green-600 hover:bg-green-700"
                >
                  <UserCheck className="mr-2 h-4 w-4" />
                  {checkInMutation.isPending ? 'Registrando...' : 'Registrar Entrada'}
                </Button>
              )}

              {guest.data.status === 'CHECKED_IN' && (
                <Button
                  onClick={handleCheckOut}
                  disabled={checkOutMutation.isPending}
                  variant="outline"
                  className="rounded-xl border-blue-200 text-blue-600 hover:bg-blue-50"
                >
                  <UserX className="mr-2 h-4 w-4" />
                  {checkOutMutation.isPending ? 'Procesando...' : 'Registrar Salida'}
                </Button>
              )}

              <Button
                variant="outline"
                onClick={onClose}
                className="rounded-xl"
              >
                Cerrar
              </Button>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <User className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900">Invitado no encontrado</h3>
            <p className="mt-2 text-gray-500">
              No se pudo cargar la información del invitado.
            </p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
