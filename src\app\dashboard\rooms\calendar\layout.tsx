import React from 'react';
import { redirect } from 'next/navigation';
import { getSession } from '@/actions/getSession';

export default async function AdminCalendarLayout({
  children
}: {
  children: React.ReactNode
}) {
  const session = await getSession();

  // Temporary admin check by email until role system is fully implemented
  const isAdmin = session.user.role === 'ADMIN';

  if (!isAdmin) {
    // Redirect to main rooms page if not admin
    redirect('/dashboard/rooms');
  }

  return <>{children}</>;
}
