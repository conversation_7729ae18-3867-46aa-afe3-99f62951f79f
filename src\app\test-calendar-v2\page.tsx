'use client';

import { useState } from 'react';
import { BookingCalendarV2 } from '@/components/calendar/BookingCalendarV2';
import { BookingEvent, CreateBookingFormData } from '@/components/calendar/types';

// Mock data for testing
const mockEvents: BookingEvent[] = [
  {
    id: '1',
    start: new Date(2024, 11, 16, 9, 0), // December 16, 2024, 9:00 AM
    end: new Date(2024, 11, 16, 10, 30),
    title: 'Reunión de equipo',
    booking: {
      id: '1',
      title: 'Reunión de equipo',
      startDate: new Date(2024, 11, 16, 9, 0),
      endDate: new Date(2024, 11, 16, 10, 30),
      status: 'CONFIRMED',
      attendees: 5,
      description: 'Revisión semanal del proyecto',
      meetingRoom: { id: '1', name: '<PERSON><PERSON> Principal' }
    }
  },
  {
    id: '2',
    start: new Date(2024, 11, 17, 14, 0), // December 17, 2024, 2:00 PM
    end: new Date(2024, 11, 17, 15, 0),
    title: 'Presentación cliente',
    booking: {
      id: '2',
      title: 'Presentación cliente',
      startDate: new Date(2024, 11, 17, 14, 0),
      endDate: new Date(2024, 11, 17, 15, 0),
      status: 'CONFIRMED',
      attendees: 3,
      description: 'Demo del producto final',
      meetingRoom: { id: '2', name: 'Sala de Conferencias' }
    }
  }
];

const mockMeetingRooms = [
  {
    id: '1',
    name: 'Sala Principal',
    capacity: 10,
    location: 'Piso 1',
    equipment: ['Proyector', 'Pizarra', 'Video conferencia']
  },
  {
    id: '2',
    name: 'Sala de Conferencias',
    capacity: 20,
    location: 'Piso 2',
    equipment: ['Proyector', 'Sistema de audio', 'Video conferencia', 'Pizarra digital']
  },
  {
    id: '3',
    name: 'Sala Pequeña',
    capacity: 6,
    location: 'Piso 1',
    equipment: ['TV', 'Pizarra']
  },
  {
    id: '4',
    name: 'Sala Ejecutiva',
    capacity: 8,
    location: 'Piso 3',
    equipment: ['Proyector', 'Mesa ejecutiva', 'Video conferencia']
  }
];

export default function TestCalendarV2Page() {
  const [events, setEvents] = useState<BookingEvent[]>(mockEvents);
  const [isCreatingBooking, setIsCreatingBooking] = useState(false);
  const [isCancellingBooking, setIsCancellingBooking] = useState(false);

  const handleCreateBooking = async (data: CreateBookingFormData) => {
    setIsCreatingBooking(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Create new booking event
      const selectedRoom = mockMeetingRooms.find(room => room.id === data.meetingRoomId);
      const startDateTime = new Date(data.selectedDate);
      const endDateTime = new Date(data.selectedDate);
      
      // Parse time strings and set hours/minutes
      const [startHour, startMinute] = data.startTime.split(':').map(Number);
      const [endHour, endMinute] = data.endTime.split(':').map(Number);
      
      startDateTime.setHours(startHour, startMinute, 0, 0);
      endDateTime.setHours(endHour, endMinute, 0, 0);
      
      const newBooking = {
        id: Date.now().toString(),
        title: data.title,
        startDate: startDateTime,
        endDate: endDateTime,
        status: 'CONFIRMED' as const,
        attendees: data.attendees,
        description: data.description || '',
        meetingRoom: selectedRoom
      };
      
      const newEvent: BookingEvent = {
        id: newBooking.id,
        start: startDateTime,
        end: endDateTime,
        title: data.title,
        booking: newBooking
      };
      
      setEvents(prev => [...prev, newEvent]);
      
      console.log('Booking created:', newBooking);
    } catch (error) {
      console.error('Error creating booking:', error);
      throw error;
    } finally {
      setIsCreatingBooking(false);
    }
  };

  const handleCancelBooking = async (booking: any) => {
    setIsCancellingBooking(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Remove booking from events
      setEvents(prev => prev.filter(event => event.booking?.id !== booking.id));
      
      console.log('Booking cancelled:', booking);
    } catch (error) {
      console.error('Error cancelling booking:', error);
      throw error;
    } finally {
      setIsCancellingBooking(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Calendario V2 - Prueba de Popover
          </h1>
          <p className="text-gray-600">
            Prueba el nuevo calendario con popover inteligente en desktop y modal en mobile.
          </p>
          <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h3 className="font-semibold text-blue-900 mb-2">Instrucciones:</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• <strong>Desktop (≥768px):</strong> Haz clic en un slot vacío para ver el popover</li>
              <li>• <strong>Mobile (&lt;768px):</strong> Haz clic en un slot vacío para ver el modal</li>
              <li>• El popover se posiciona automáticamente según el espacio disponible</li>
              <li>• La selección se mantiene visible mientras el formulario está abierto</li>
              <li>• Puedes arrastrar para seleccionar múltiples slots</li>
            </ul>
          </div>
        </div>

        <BookingCalendarV2
          events={events}
          meetingRooms={mockMeetingRooms}
          onCreateBooking={handleCreateBooking}
          onCancelBooking={handleCancelBooking}
          isCreatingBooking={isCreatingBooking}
          isCancellingBooking={isCancellingBooking}
        />
      </div>
    </div>
  );
}
