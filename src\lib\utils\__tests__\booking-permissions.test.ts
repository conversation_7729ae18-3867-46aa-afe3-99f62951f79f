/**
 * Test file for booking permissions utilities
 */

import { canViewBookingDetails, canInteractWithBooking, getBookingDisplayTitle, getBookingEventStyle } from '../booking-permissions';

// Mock booking data for testing
const mockBookings = {
  // User is creator
  ownBooking: {
    id: '1',
    userId: 'user123',
    title: 'Mi Reunión',
    status: 'CONFIRMED',
    attendees: {
      internal: ['user456'],
      external: ['<EMAIL>']
    }
  },
  
  // User is attendee
  attendeeBooking: {
    id: '2',
    userId: 'user456',
    title: 'Reunión de Equipo',
    status: 'CONFIRMED',
    attendees: {
      internal: ['user123', 'user789'],
      external: []
    }
  },
  
  // User has no access
  restrictedBooking: {
    id: '3',
    userId: 'user456',
    title: 'Reunión Privada',
    status: 'CONFIRMED',
    attendees: {
      internal: ['user789'],
      external: ['<EMAIL>']
    }
  }
};

const currentUserId = 'user123';

describe('Booking Permissions', () => {
  describe('canViewBookingDetails', () => {
    test('should allow creator to view booking details', () => {
      expect(canViewBookingDetails(mockBookings.ownBooking, currentUserId)).toBe(true);
    });

    test('should allow attendee to view booking details', () => {
      expect(canViewBookingDetails(mockBookings.attendeeBooking, currentUserId)).toBe(true);
    });

    test('should deny access to restricted booking', () => {
      expect(canViewBookingDetails(mockBookings.restrictedBooking, currentUserId)).toBe(false);
    });

    test('should handle null/undefined booking', () => {
      expect(canViewBookingDetails(null as any, currentUserId)).toBe(false);
      expect(canViewBookingDetails(undefined as any, currentUserId)).toBe(false);
    });

    test('should handle null/undefined userId', () => {
      expect(canViewBookingDetails(mockBookings.ownBooking, null as any)).toBe(false);
      expect(canViewBookingDetails(mockBookings.ownBooking, undefined as any)).toBe(false);
    });
  });

  describe('canInteractWithBooking', () => {
    test('should allow creator to interact with booking', () => {
      expect(canInteractWithBooking(mockBookings.ownBooking, currentUserId)).toBe(true);
    });

    test('should allow attendee to interact with booking', () => {
      expect(canInteractWithBooking(mockBookings.attendeeBooking, currentUserId)).toBe(true);
    });

    test('should deny interaction with restricted booking', () => {
      expect(canInteractWithBooking(mockBookings.restrictedBooking, currentUserId)).toBe(false);
    });
  });

  describe('getBookingDisplayTitle', () => {
    test('should show real title for accessible booking', () => {
      expect(getBookingDisplayTitle(mockBookings.ownBooking, currentUserId)).toBe('Mi Reunión');
      expect(getBookingDisplayTitle(mockBookings.attendeeBooking, currentUserId)).toBe('Reunión de Equipo');
    });

    test('should show "Ocupado" for restricted booking', () => {
      expect(getBookingDisplayTitle(mockBookings.restrictedBooking, currentUserId)).toBe('Ocupado');
    });

    test('should handle booking without title', () => {
      const bookingWithoutTitle = { ...mockBookings.ownBooking, title: undefined };
      expect(getBookingDisplayTitle(bookingWithoutTitle, currentUserId)).toBe('Sin título');
    });
  });

  describe('getBookingEventStyle', () => {
    test('should return selection style for selection events', () => {
      const style = getBookingEventStyle(mockBookings.ownBooking, currentUserId, true);
      expect(style.backgroundColor).toBe('rgba(59, 130, 246, 0.2)');
      expect(style.pointerEvents).toBe('none');
    });

    test('should return restricted style for non-accessible booking', () => {
      const style = getBookingEventStyle(mockBookings.restrictedBooking, currentUserId, false);
      expect(style.backgroundColor).toBe('#f3f4f6');
      expect(style.color).toBe('#6b7280');
      expect(style.cursor).toBe('not-allowed');
      expect(style.pointerEvents).toBe('none');
    });

    test('should return normal style for accessible booking', () => {
      const style = getBookingEventStyle(mockBookings.ownBooking, currentUserId, false);
      expect(style.backgroundColor).toBe('#eff6ff'); // Light blue for CONFIRMED
      expect(style.border).toBe('2px solid #3b82f6'); // Blue border for CONFIRMED
      expect(style.cursor).toBe('pointer');
      expect(style.pointerEvents).toBeUndefined();
    });

    test('should handle different booking statuses', () => {
      const completedBooking = { ...mockBookings.ownBooking, status: 'COMPLETED' };
      const style = getBookingEventStyle(completedBooking, currentUserId, false);
      expect(style.backgroundColor).toBe('#f0fdf4'); // Light green
      expect(style.border).toBe('2px solid #10b981'); // Green border
    });
  });
});

console.log('Booking permissions tests defined. Run with: npm test booking-permissions.test.ts');
