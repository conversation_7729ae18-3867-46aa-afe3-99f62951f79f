'use client';

import { <PERSON><PERSON>, <PERSON>alogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar, Clock, MapPin, Plus, X } from 'lucide-react';
import { DateTime } from 'luxon';
import { useForm } from 'react-hook-form';
import { useState, useEffect } from 'react';
import { CreateBookingModalProps, CreateBookingFormData } from './types';

export function CreateBookingModal({
  isOpen,
  onClose,
  selectedSlot,
  meetingRooms,
  onSubmit,
  isLoading = false
}: CreateBookingModalProps) {
  const [internalAttendees, setInternalAttendees] = useState<string[]>([]);
  const [externalAttendees, setExternalAttendees] = useState<string[]>([]);
  const [newInternalAttendee, setNewInternalAttendee] = useState('');
  const [newExternalAttendee, setNewExternalAttendee] = useState('');
  // const [selectedRoom, setSelectedRoom] = useState<any>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<CreateBookingFormData>();

  const watchedRoomId = watch('resourceId');

  // Update selected room when room ID changes and adjust end time if needed
  useEffect(() => {
    if (watchedRoomId && selectedSlot) {
      const room = meetingRooms.find(r => r.id === watchedRoomId);
      // setSelectedRoom(room);

      // Adjust end time if the selected duration doesn't meet minimum booking duration
      if (room?.bookingRules?.minBookingDuration) {
        const currentStartTime = watch('startTime');
        const currentEndTime = watch('endTime');

        if (currentStartTime && currentEndTime) {
          const [startHour, startMinute] = currentStartTime.split(':').map(Number);
          const [endHour, endMinute] = currentEndTime.split(':').map(Number);

          const start = DateTime.fromJSDate(selectedSlot.start).set({
            hour: startHour,
            minute: startMinute
          });
          const end = DateTime.fromJSDate(selectedSlot.start).set({
            hour: endHour,
            minute: endMinute
          });

          const durationMinutes = end.diff(start, 'minutes').minutes;

          // If current duration is less than minimum, adjust end time
          if (durationMinutes < room.bookingRules.minBookingDuration) {
            const adjustedEnd = start.plus({ minutes: room.bookingRules.minBookingDuration });
            setValue('endTime', adjustedEnd.toFormat('HH:mm'));
          }
        }
      }
    }
  }, [watchedRoomId, meetingRooms, selectedSlot, watch, setValue]);

  useEffect(() => {
    if (isOpen && selectedSlot) {
      const startTime = DateTime.fromJSDate(selectedSlot.start).toFormat('HH:mm');
      const endTime = DateTime.fromJSDate(selectedSlot.end).toFormat('HH:mm');

      setValue('startTime', startTime);
      setValue('endTime', endTime);

      if (selectedSlot.resourceId) {
        setValue('resourceId', selectedSlot.resourceId);
      }
    }
  }, [isOpen, selectedSlot, setValue]);

  const handleClose = () => {
    reset();
    setInternalAttendees([]);
    setExternalAttendees([]);
    setNewInternalAttendee('');
    setNewExternalAttendee('');
    // setSelectedRoom(null);
    onClose();
  };

  const addInternalAttendee = () => {
    if (newInternalAttendee.trim() && !internalAttendees.includes(newInternalAttendee.trim())) {
      const updated = [...internalAttendees, newInternalAttendee.trim()];
      setInternalAttendees(updated);
      setNewInternalAttendee('');
    }
  };

  const addExternalAttendee = () => {
    if (newExternalAttendee.trim() && !externalAttendees.includes(newExternalAttendee.trim())) {
      const updated = [...externalAttendees, newExternalAttendee.trim()];
      setExternalAttendees(updated);
      setNewExternalAttendee('');
    }
  };

  const removeInternalAttendee = (attendee: string) => {
    setInternalAttendees(prev => prev.filter(a => a !== attendee));
  };

  const removeExternalAttendee = (attendee: string) => {
    setExternalAttendees(prev => prev.filter(a => a !== attendee));
  };

  const onSubmitForm = async (data: CreateBookingFormData) => {
    const formData = {
      ...data,
      internalAttendees: internalAttendees.filter(email => email.trim() !== ''),
      externalAttendees: externalAttendees.filter(email => email.trim() !== '')
    };

    await onSubmit(formData);
    handleClose();
  };



  if (!selectedSlot) return null;

  const selectedDate = DateTime.fromJSDate(selectedSlot.start);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-purple-600" />
            Nueva Reserva
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmitForm)} className="space-y-6">
          {/* Date and Time Info */}
          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-purple-600" />
                <span className="font-medium">
                  {selectedDate.toFormat('dd/MM/yyyy')}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-purple-600" />
                <span>
                  {selectedDate.toFormat('HH:mm')} - {DateTime.fromJSDate(selectedSlot.end).toFormat('HH:mm')}
                </span>
              </div>
            </div>
          </div>

          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <Label htmlFor="title">Título de la reunión *</Label>
              <Input
                id="title"
                {...register('title', { required: 'El título es requerido' })}
                placeholder="Ej: Reunión de equipo"
              />
              {errors.title && (
                <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="resourceId">Sala de reuniones *</Label>
              <Select onValueChange={(value) => setValue('resourceId', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar sala" />
                </SelectTrigger>
                <SelectContent>
                  {meetingRooms?.map((room) => (
                    <SelectItem key={room.id} value={room.id}>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        {room.name} (Cap: {room.capacity})
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.resourceId && (
                <p className="text-sm text-red-600 mt-1">Selecciona una sala</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label htmlFor="startTime">Hora inicio</Label>
                <Input
                  id="startTime"
                  type="time"
                  {...register('startTime', { required: true })}
                />
              </div>
              <div>
                <Label htmlFor="endTime">Hora fin</Label>
                <Input
                  id="endTime"
                  type="time"
                  {...register('endTime', { required: true })}
                />
              </div>
            </div>
          </div>

          {/* Description */}
          <div>
            <Label htmlFor="description">Descripción</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Descripción opcional de la reunión"
              rows={3}
            />
          </div>

          {/* Attendees */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Asistentes</h4>
            <Tabs defaultValue="internal" className="space-y-4">
              <TabsList className="grid w-full grid-cols-2 rounded-xl bg-gray-100">
                <TabsTrigger value="internal" className="rounded-lg">Internos</TabsTrigger>
                <TabsTrigger value="external" className="rounded-lg">Externos</TabsTrigger>
              </TabsList>

              <TabsContent value="internal" className="space-y-3">
                <div className="flex gap-2">
                  <Input
                    value={newInternalAttendee}
                    onChange={(e) => setNewInternalAttendee(e.target.value)}
                    placeholder="Email del empleado..."
                    className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                    onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addInternalAttendee())}
                  />
                  <Button
                    type="button"
                    onClick={addInternalAttendee}
                    variant="outline"
                    className="rounded-xl border-purple-200 text-purple-600 hover:bg-purple-50"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                {internalAttendees.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {internalAttendees.map((attendee) => (
                      <div
                        key={attendee}
                        className="bg-blue-100 text-blue-700 hover:bg-blue-200 rounded-full px-3 py-1 flex items-center gap-2"
                      >
                        {attendee}
                        <button
                          type="button"
                          onClick={() => removeInternalAttendee(attendee)}
                          className="hover:text-blue-900"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="external" className="space-y-3">
                <div className="flex gap-2">
                  <Input
                    value={newExternalAttendee}
                    onChange={(e) => setNewExternalAttendee(e.target.value)}
                    placeholder="Email del invitado externo..."
                    className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                    onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addExternalAttendee())}
                  />
                  <Button
                    type="button"
                    onClick={addExternalAttendee}
                    variant="outline"
                    className="rounded-xl border-purple-200 text-purple-600 hover:bg-purple-50"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                {externalAttendees.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {externalAttendees.map((attendee) => (
                      <div
                        key={attendee}
                        className="bg-green-100 text-green-700 hover:bg-green-200 rounded-full px-3 py-1 flex items-center gap-2"
                      >
                        {attendee}
                        <button
                          type="button"
                          onClick={() => removeExternalAttendee(attendee)}
                          className="hover:text-green-900"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancelar
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Creando...' : 'Crear Reserva'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
