/**
 * Utility functions for booking permissions and access control
 */

export interface BookingData {
  id: string;
  userId: string; // Creator of the booking
  attendees?: {
    internal?: string[]; // Array of user IDs
    external?: string[]; // Array of external emails
  };
  [key: string]: any;
}

/**
 * Check if a user can view the details of a booking
 * @param booking - The booking data
 * @param currentUserId - The current user's ID
 * @returns true if user can view details, false otherwise
 */
export function canViewBookingDetails(booking: BookingData, currentUserId: string): boolean {
  if (!booking || !currentUserId) {
    return false;
  }

  // User is the creator
  if (booking.userId === currentUserId) {
    return true;
  }

  // User is in the internal attendees list
  if (booking.attendees?.internal?.includes(currentUserId)) {
    return true;
  }

  // User cannot view details
  return false;
}

/**
 * Check if a user can interact with a booking (click, edit, cancel)
 * @param booking - The booking data
 * @param currentUserId - The current user's ID
 * @returns true if user can interact, false otherwise
 */
export function canInteractWithBooking(booking: BookingData, currentUserId: string): boolean {
  return canViewBookingDetails(booking, currentUserId);
}

/**
 * Get the appropriate style for a booking event based on user permissions
 * @param booking - The booking data
 * @param currentUserId - The current user's ID
 * @param isSelection - Whether this is a selection event
 * @returns Style object for the event
 */
export function getBookingEventStyle(booking: BookingData, currentUserId: string, isSelection = false) {
  // Handle temporary selection event
  if (isSelection) {
    return {
      backgroundColor: 'rgba(59, 130, 246, 0.2)', // Light blue background
      borderRadius: '4px',
      border: '2px dashed #3b82f6', // Blue dashed border
      color: 'transparent', // Hide any text
      fontWeight: '500',
      boxShadow: '0 1px 3px rgba(59, 130, 246, 0.3)',
      padding: '4px 8px',
      lineHeight: '1.3',
      pointerEvents: 'none' as const // Prevent interaction with selection event
    };
  }

  const canView = canViewBookingDetails(booking, currentUserId);

  if (!canView) {
    // Blocked/restricted booking style - gray and non-interactive
    return {
      backgroundColor: '#f3f4f6', // Light gray background
      borderRadius: '4px',
      border: '1px solid #d1d5db', // Gray border
      color: '#6b7280', // Gray text
      fontWeight: '400',
      opacity: '0.7',
      padding: '4px 8px',
      lineHeight: '1.3',
      cursor: 'not-allowed',
      pointerEvents: 'none' as const // Prevent interaction
    };
  }

  // Default accessible booking style based on status
  let borderColor = '#6b7280'; // Default gray
  let backgroundColor = '#ffffff'; // Default white

  switch (booking?.status) {
    case 'CONFIRMED':
      borderColor = '#3b82f6'; // Blue
      backgroundColor = '#eff6ff'; // Light blue
      break;
    case 'COMPLETED':
      borderColor = '#10b981'; // Green
      backgroundColor = '#f0fdf4'; // Light green
      break;
    case 'CANCELLED':
      borderColor = '#ef4444'; // Red
      backgroundColor = '#fef2f2'; // Light red
      break;
    case 'NO_SHOW':
      borderColor = '#6b7280'; // Gray
      backgroundColor = '#f9fafb'; // Light gray
      break;
  }

  return {
    backgroundColor,
    borderRadius: '4px',
    border: `2px solid ${borderColor}`,
    color: '#1f2937', // Dark gray text
    fontWeight: '500',
    padding: '4px 8px',
    lineHeight: '1.3',
    cursor: 'pointer'
  };
}

/**
 * Get the display title for a booking based on user permissions
 * @param booking - The booking data
 * @param currentUserId - The current user's ID
 * @returns The title to display
 */
export function getBookingDisplayTitle(booking: BookingData, currentUserId: string): string {
  const canView = canViewBookingDetails(booking, currentUserId);
  
  if (!canView) {
    return 'Ocupado'; // Generic "Busy" text for blocked slots
  }
  
  return booking.title || 'Sin título';
}
