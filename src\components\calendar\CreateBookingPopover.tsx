'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { DateTime } from 'luxon';
import { X, Calendar, Clock, MapPin, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SelectedSlot, CreateBookingFormData } from './types';

interface CreateBookingPopoverProps {
  isOpen: boolean;
  onClose: () => void;
  selectedSlot: SelectedSlot | null;
  meetingRooms: any[];
  onSubmit: (data: CreateBookingFormData) => Promise<void>;
  isLoading?: boolean;
  position: { x: number; y: number };
}

export function CreateBookingPopover({
  isOpen,
  onClose,
  selectedSlot,
  meetingRooms,
  onSubmit,
  isLoading = false,
  position
}: CreateBookingPopoverProps) {
  // const [selectedRoom, setSelectedRoom] = useState<any>(null);
  const [internalAttendees, setInternalAttendees] = useState<string[]>([]);
  const [externalAttendees, setExternalAttendees] = useState<string[]>([]);
  const [newInternalAttendee, setNewInternalAttendee] = useState('');
  const [newExternalAttendee, setNewExternalAttendee] = useState('');

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors }
  } = useForm<CreateBookingFormData>();

  const watchedRoomId = watch('resourceId');

  // Update selected room when room ID changes and adjust end time if needed
  useEffect(() => {
    if (watchedRoomId && selectedSlot) {
      const room = meetingRooms.find(r => r.id === watchedRoomId);
      // setSelectedRoom(room);

      // Adjust end time if the selected duration doesn't meet minimum booking duration
      if (room?.bookingRules?.minBookingDuration) {
        const currentStartTime = watch('startTime');
        const currentEndTime = watch('endTime');

        if (currentStartTime && currentEndTime) {
          const [startHour, startMinute] = currentStartTime.split(':').map(Number);
          const [endHour, endMinute] = currentEndTime.split(':').map(Number);

          const start = DateTime.fromJSDate(selectedSlot.start).set({
            hour: startHour,
            minute: startMinute
          });
          const end = DateTime.fromJSDate(selectedSlot.start).set({
            hour: endHour,
            minute: endMinute
          });

          const durationMinutes = end.diff(start, 'minutes').minutes;

          // If current duration is less than minimum, adjust end time
          if (durationMinutes < room.bookingRules.minBookingDuration) {
            const adjustedEnd = start.plus({ minutes: room.bookingRules.minBookingDuration });
            setValue('endTime', adjustedEnd.toFormat('HH:mm'));
          }
        }
      }
    }
  }, [watchedRoomId, meetingRooms, selectedSlot, watch, setValue]);

  // Set default values when slot is selected
  useEffect(() => {
    if (selectedSlot && isOpen) {
      const startTime = DateTime.fromJSDate(selectedSlot.start).toFormat('HH:mm');
      let endTime = DateTime.fromJSDate(selectedSlot.end).toFormat('HH:mm');

      // Check if there's already a room selected and apply minimum duration
      const currentRoomId = watch('resourceId');
      if (currentRoomId) {
        const currentRoom = meetingRooms.find(r => r.id === currentRoomId);
        if (currentRoom?.bookingRules?.minBookingDuration) {
          const start = DateTime.fromJSDate(selectedSlot.start);
          const end = DateTime.fromJSDate(selectedSlot.end);
          const durationMinutes = end.diff(start, 'minutes').minutes;

          // If the selected duration is less than minimum, adjust the end time
          if (durationMinutes < currentRoom.bookingRules.minBookingDuration) {
            const adjustedEnd = start.plus({ minutes: currentRoom.bookingRules.minBookingDuration });
            endTime = adjustedEnd.toFormat('HH:mm');
          }
        }
      }

      setValue('startTime', startTime);
      setValue('endTime', endTime);

      // Reset other fields
      setValue('title', '');
      setValue('description', '');

      // Only reset resourceId if there's no current selection or if the slot has a specific resourceId
      if (!currentRoomId || selectedSlot.resourceId) {
        setValue('resourceId', selectedSlot.resourceId || '');
      }

      // setSelectedRoom(null);
    }
  }, [selectedSlot, isOpen, setValue, watch, meetingRooms]);

  // Reset form when popover closes
  useEffect(() => {
    if (!isOpen) {
      reset();
      // setSelectedRoom(null);
      setInternalAttendees([]);
      setExternalAttendees([]);
      setNewInternalAttendee('');
      setNewExternalAttendee('');
    }
  }, [isOpen, reset]);

  // Functions to handle attendees
  const addInternalAttendee = () => {
    if (newInternalAttendee.trim() && !internalAttendees.includes(newInternalAttendee.trim())) {
      const updated = [...internalAttendees, newInternalAttendee.trim()];
      setInternalAttendees(updated);
      setNewInternalAttendee('');
    }
  };

  const addExternalAttendee = () => {
    if (newExternalAttendee.trim() && !externalAttendees.includes(newExternalAttendee.trim())) {
      const updated = [...externalAttendees, newExternalAttendee.trim()];
      setExternalAttendees(updated);
      setNewExternalAttendee('');
    }
  };

  const removeInternalAttendee = (attendee: string) => {
    setInternalAttendees(prev => prev.filter(a => a !== attendee));
  };

  const removeExternalAttendee = (attendee: string) => {
    setExternalAttendees(prev => prev.filter(a => a !== attendee));
  };

  const handleFormSubmit = async (data: CreateBookingFormData) => {
    try {
      const formData = {
        ...data,
        internalAttendees: internalAttendees.filter(email => email.trim() !== ''),
        externalAttendees: externalAttendees.filter(email => email.trim() !== '')
      };
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting form:', error);
      // Error handling is done in parent component
    }
  };

  // Close popover when clicking outside
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      const popover = document.getElementById('booking-popover');

      // Don't close if clicking on calendar elements, the popover itself, or Select elements
      if (popover && !popover.contains(target)) {
        // Check if click is on calendar elements
        const isCalendarClick = target.closest('.rbc-calendar') ||
          target.closest('.rbc-event') ||
          target.closest('.rbc-time-slot');

        // Check if click is on Select dropdown elements (which render in portals)
        const isSelectClick = target.closest('[data-radix-select-content]') ||
          target.closest('[data-radix-select-item]') ||
          target.closest('[data-radix-select-viewport]') ||
          target.closest('[data-radix-popper-content-wrapper]');

        if (!isCalendarClick && !isSelectClick) {
          onClose();
        }
      }
    };

    // Add delay to prevent immediate closing when opening
    const timer = setTimeout(() => {
      document.addEventListener('mousedown', handleClickOutside);
    }, 200);

    return () => {
      clearTimeout(timer);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Close on Escape key
  useEffect(() => {
    if (!isOpen) return;

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  if (!isOpen || !selectedSlot) return null;

  const selectedDate = DateTime.fromJSDate(selectedSlot.start);
  const formattedDate = selectedDate.toFormat('EEEE, dd \'de\' MMMM \'de\' yyyy');

  return (
    <div
      id="booking-popover"
      className="fixed z-[9999] w-96 bg-white rounded-lg shadow-2xl border border-gray-200"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        maxHeight: 'min(500px, 80vh)',
        overflowY: 'auto',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
      }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <Calendar className="h-5 w-5 text-purple-600" />
          <h3 className="text-lg font-semibold text-gray-900">Nueva Reserva</h3>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-8 w-8 p-0 hover:bg-gray-100"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Content */}
      <form onSubmit={handleSubmit(handleFormSubmit)} className="p-4 space-y-4">
        {/* Date and Time Info */}
        <div className="bg-purple-50 rounded-lg p-3 space-y-2">
          <div className="flex items-center gap-2 text-sm text-purple-700">
            <Calendar className="h-4 w-4" />
            <span className="font-medium">{formattedDate}</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-purple-700">
            <Clock className="h-4 w-4" />
            <span>{watch('startTime')} - {watch('endTime')}</span>
          </div>
        </div>

        {/* Basic Info */}
        <div className="grid grid-cols-1 gap-4">
          <div>
            <Label htmlFor="title">Título de la reunión *</Label>
            <Input
              id="title"
              {...register('title', { required: 'El título es requerido' })}
              placeholder="Ej: Reunión de equipo"
            />
            {errors.title && (
              <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="resourceId">Sala de reuniones *</Label>
            <Select onValueChange={(value) => setValue('resourceId', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Seleccionar sala" />
              </SelectTrigger>
              <SelectContent className="z-[10000]">
                {meetingRooms?.map((room) => (
                  <SelectItem key={room.id} value={room.id}>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      {room.name} (Cap: {room.capacity})
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.resourceId && (
              <p className="text-sm text-red-600 mt-1">Selecciona una sala</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label htmlFor="startTime">Hora inicio</Label>
              <Input
                id="startTime"
                type="time"
                {...register('startTime', { required: true })}
              />
            </div>
            <div>
              <Label htmlFor="endTime">Hora fin</Label>
              <Input
                id="endTime"
                type="time"
                {...register('endTime', { required: true })}
              />
            </div>
          </div>
        </div>

        {/* Description */}
        <div>
          <Label htmlFor="description">Descripción</Label>
          <Textarea
            id="description"
            {...register('description')}
            placeholder="Descripción opcional de la reunión"
            rows={2}
          />
        </div>

        {/* Attendees */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900">Asistentes</h4>
          <Tabs defaultValue="internal" className="space-y-4">
            <TabsList className="grid w-full grid-cols-2 rounded-xl bg-gray-100">
              <TabsTrigger value="internal" className="rounded-lg">Internos</TabsTrigger>
              <TabsTrigger value="external" className="rounded-lg">Externos</TabsTrigger>
            </TabsList>

            <TabsContent value="internal" className="space-y-3">
              <div className="flex gap-2">
                <Input
                  value={newInternalAttendee}
                  onChange={(e) => setNewInternalAttendee(e.target.value)}
                  placeholder="Email del empleado..."
                  className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addInternalAttendee())}
                />
                <Button
                  type="button"
                  onClick={addInternalAttendee}
                  variant="outline"
                  className="rounded-xl border-purple-200 text-purple-600 hover:bg-purple-50"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {internalAttendees.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {internalAttendees.map((attendee) => (
                    <div
                      key={attendee}
                      className="bg-blue-100 text-blue-700 hover:bg-blue-200 rounded-full px-3 py-1 flex items-center gap-2"
                    >
                      {attendee}
                      <button
                        type="button"
                        onClick={() => removeInternalAttendee(attendee)}
                        className="hover:text-blue-900"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="external" className="space-y-3">
              <div className="flex gap-2">
                <Input
                  value={newExternalAttendee}
                  onChange={(e) => setNewExternalAttendee(e.target.value)}
                  placeholder="Email del invitado externo..."
                  className="rounded-xl border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addExternalAttendee())}
                />
                <Button
                  type="button"
                  onClick={addExternalAttendee}
                  variant="outline"
                  className="rounded-xl border-purple-200 text-purple-600 hover:bg-purple-50"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {externalAttendees.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {externalAttendees.map((attendee) => (
                    <div
                      key={attendee}
                      className="bg-green-100 text-green-700 hover:bg-green-200 rounded-full px-3 py-1 flex items-center gap-2"
                    >
                      {attendee}
                      <button
                        type="button"
                        onClick={() => removeExternalAttendee(attendee)}
                        className="hover:text-green-900"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            className="flex-1"
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button
            type="submit"
            className="flex-1 bg-purple-600 hover:bg-purple-700"
            disabled={isLoading}
          >
            {isLoading ? 'Creando...' : 'Crear Reserva'}
          </Button>
        </div>
      </form>
    </div>
  );
}
