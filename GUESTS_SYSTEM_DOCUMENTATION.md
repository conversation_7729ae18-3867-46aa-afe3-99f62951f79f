# Sistema de Gestión de Invitados - OCN House

## Descripción General

El sistema de gestión de invitados permite administrar invitaciones externas tanto para visitas generales como para reservas específicas de salas de reuniones o escritorios. Está diseñado para facilitar el trabajo de recepción y empleados.

## Características Principales

### 1. Tipos de Invitados

#### Invitados Generales
- Invitaciones sin reserva específica
- Para visitas comerciales, reuniones informales, etc.
- Solo requieren datos básicos del visitante y fecha/hora

#### Invitados de Reservas
- Vinculados a una reserva específica (sala de reuniones o escritorio)
- Incluyen detalles de la reserva y ubicación
- Automáticamente asociados al evento en el calendario

### 2. Funcionalidades por Rol

#### Para Empleados
- **Crear invitaciones generales**: Invitar personas sin necesidad de reservar un espacio
- **Crear invitaciones para reservas**: Al hacer una reserva, pueden agregar invitados externos
- **Gestionar sus propios invitados**: Ver, editar y cancelar invitaciones que han creado

#### Para Recepción
- **Vista diaria de invitados**: Lista de todos los invitados esperados para el día actual
- **Búsqueda rápida**: Buscar invitados por email, nombre o empresa
- **Check-in/Check-out**: Registrar entrada y salida de invitados
- **Gestión de estados**: Marcar como "no se presentó" si es necesario

### 3. Estados de Invitados

- **PENDING**: Invitado esperado, aún no ha llegado
- **CHECKED_IN**: Invitado registrado y presente en las instalaciones
- **CHECKED_OUT**: Invitado que ya se retiró
- **NO_SHOW**: Invitado que no se presentó en la fecha programada

## Interfaz de Usuario

### Página Principal (/dashboard/guests)

#### Filtros Disponibles
- **Búsqueda**: Por nombre, email o empresa
- **Estado**: Filtrar por estado del invitado
- **Fecha**: 
  - Hoy (por defecto)
  - Esta semana
  - Este mes
  - Rango personalizado

#### Lista de Invitados
Cada entrada muestra:
- Nombre del invitado y estado
- Email y información de contacto
- Fecha y hora de la visita
- Tipo de invitación (General o vinculada a reserva)
- Empresa (si aplica)
- Acciones disponibles según el estado

#### Acciones Disponibles
- **Registrar**: Para invitados pendientes (check-in)
- **Salida**: Para invitados registrados (check-out)
- **Ver detalles**: Modal con información completa
- **Editar**: Modificar información del invitado
- **Eliminar**: Cancelar invitación

### Modal de Creación de Invitados

#### Campos Requeridos
- Nombre completo
- Email

#### Campos Opcionales
- Teléfono
- Empresa
- Hora específica (por defecto usa hora actual)
- Instrucciones especiales

#### Validaciones
- Email válido
- Fecha no puede ser en el pasado
- Al menos email o teléfono debe estar presente

### Modal de Detalles

Muestra información completa del invitado incluyendo:
- Datos de contacto completos
- Información de la visita
- Detalles de reserva (si aplica)
- Instrucciones especiales
- Historial de check-in/check-out
- Acciones disponibles según el estado

## API Endpoints

### Endpoints Principales

#### GET /api/guests
- Lista paginada de invitados con filtros
- Parámetros: page, limit, search, status, startDate, endDate, visitDate

#### GET /api/guests/daily
- Invitados para una fecha específica (por defecto hoy)
- Optimizado para uso de recepción
- Parámetros: date, status

#### POST /api/guests
- Crear nueva invitación
- Requiere autenticación (hostId del usuario)

#### GET /api/guests/:id
- Detalles de un invitado específico

#### PUT /api/guests/:id
- Actualizar información del invitado

#### DELETE /api/guests/:id
- Eliminar invitación

#### POST /api/guests/:id/check-in
- Registrar entrada del invitado

#### POST /api/guests/:id/check-out
- Registrar salida del invitado

#### POST /api/guests/:id/no-show
- Marcar como "no se presentó"

### Operaciones en Lote

#### POST /api/guests/bulk
Permite operaciones masivas:
- check-in: Registrar múltiples invitados
- check-out: Dar salida a múltiples invitados
- mark-no-show: Marcar múltiples como "no show"
- delete: Eliminar múltiples invitaciones

## Flujos de Trabajo

### Flujo de Empleado - Crear Invitación General

1. Empleado accede a /dashboard/guests
2. Hace clic en "Crear Invitación"
3. Completa formulario con datos del invitado
4. Selecciona fecha y hora de visita
5. Agrega instrucciones si es necesario
6. Guarda la invitación

### Flujo de Recepción - Gestión Diaria

1. Recepcionista accede a /dashboard/guests
2. Ve automáticamente invitados del día actual
3. Cuando llega un invitado:
   - Busca por email o nombre
   - Verifica información en modal de detalles
   - Hace check-in del invitado
4. Al finalizar la visita:
   - Hace check-out del invitado

### Flujo de Reserva con Invitados

1. Empleado crea reserva de sala/escritorio
2. Durante la creación, agrega invitados externos
3. Sistema automáticamente:
   - Crea registros de invitados vinculados a la reserva
   - Los muestra en la lista de invitados del día
   - Incluye detalles de la reserva en la información del invitado

## Consideraciones Técnicas

### Base de Datos
- Modelo Guest en Prisma con relaciones a User (host) y Booking
- Índices optimizados para búsquedas por fecha y estado
- Campos opcionales para flexibilidad

### Autenticación
- Middleware de autenticación en todos los endpoints
- Los usuarios solo pueden gestionar sus propios invitados
- Recepción puede ver todos los invitados

### Validaciones
- Validación de email en frontend y backend
- Fechas no pueden ser en el pasado
- Estados válidos según flujo de negocio

### Performance
- Paginación en listados
- Filtros optimizados con índices de base de datos
- Cache de 1-2 minutos en React Query para datos frecuentes

## Próximas Mejoras

1. **Notificaciones**: Email/SMS automáticos a invitados
2. **QR Codes**: Códigos QR para check-in automático
3. **Integración con calendario**: Sincronización con calendarios externos
4. **Reportes**: Estadísticas de visitas y uso
5. **Fotos**: Captura de foto durante check-in para seguridad
