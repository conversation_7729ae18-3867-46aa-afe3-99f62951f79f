'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Clock, Plus, ArrowRight, Building, MapPin } from 'lucide-react';
import Link from 'next/link';
import { DateTime } from 'luxon';
import { TodayBookingsProps } from './types';

export function TodayBookings({ bookings, isLoading = false, className = '' }: TodayBookingsProps) {
  const formatTime = (date: Date | string) => {
    return DateTime.fromJSDate(new Date(date)).toFormat('HH:mm');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return 'bg-blue-100 text-blue-800';
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      case 'NO_SHOW':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Reservas de Hoy</CardTitle>
          <CardDescription>
            Próximas reuniones y reservas
          </CardDescription>
        </div>
        <Button variant="outline" size="sm" asChild>
          <Link href="/dashboard/bookings">
            Ver Todas
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[200px]" />
                  <Skeleton className="h-4 w-[160px]" />
                </div>
              </div>
            ))}
          </div>
        ) : bookings.length === 0 ? (
          <div className="text-center py-6">
            <Clock className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-2 text-sm font-semibold">No hay reservas hoy</h3>
            <p className="mt-1 text-sm text-muted-foreground">
              Crea una nueva reserva para comenzar.
            </p>
            <Button className="mt-4 bg-purple-600 hover:bg-purple-700" asChild>
              <Link href="/dashboard/bookings/new">
                <Plus className="mr-2 h-4 w-4" />
                Nueva Reserva
              </Link>
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {bookings.slice(0, 5).map((booking: any) => (
              <div key={booking.id} className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                    <Building className="h-5 w-5 text-blue-600" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {booking.title}
                  </p>
                  <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span>{formatTime(booking.startDate)} - {formatTime(booking.endDate)}</span>
                    {booking.meetingRoom && (
                      <>
                        <MapPin className="h-3 w-3" />
                        <span>{booking.meetingRoom.name}</span>
                      </>
                    )}
                  </div>
                </div>
                <Badge className={getStatusColor(booking.status)}>
                  {booking.status}
                </Badge>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
